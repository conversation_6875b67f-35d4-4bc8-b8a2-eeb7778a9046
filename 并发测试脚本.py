import requests
import json
import time
import asyncio
import aiohttp
import re
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# 测试配置
SESSIONID = "8f633f7b77bb3963bb0e9316b541927c"
HOMEPAGE_URL = "https://www.toutiao.com/c/user/4229200871163068/"
TEST_ITERATIONS = 100
INTERVAL_MS = 500  # 500毫秒间隔

# API端点配置
API_ENDPOINTS = {
    "收益数据": {
        "url": "https://mp.toutiao.com/pgc/mp/income/income_statement_abstract?only_mid_income=false&days=30&app_id=1231",
        "headers": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
            "Accept": "application/json, text/plain, */*",
            "Referer": "https://mp.toutiao.com/profile_v4/analysis/income-overview",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cookie": f"sessionid={SESSIONID}"
        }
    },
    "信用分和粉丝数": {
        "url": "https://mp.toutiao.com/mp/agw/creator_project/get_benefit_page_info?app_id=1231",
        "headers": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
            "Accept": "application/json, text/plain, */*",
            "Referer": "https://mp.toutiao.com/profile_v4/analysis/works-overall/all",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cookie": f"sessionid={SESSIONID}"
        }
    },
    "账号详细信息": {
        "url": "https://mp.toutiao.com/profile_v4/index",
        "headers": {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "max-age=0",
            "Priority": "u=0, i",
            "Sec-Ch-Ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "cross-site",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "Cookie": f"sessionid={SESSIONID}"
        }
    },
    "阅读量数据": {
        "url": "https://mp.toutiao.com/mp/fe_api/home/<USER>",
        "headers": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
            "Accept": "application/json, text/plain, */*",
            "Referer": "https://mp.toutiao.com/profile_v4/index",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cookie": f"sessionid={SESSIONID}"
        }
    },
    "草稿箱数量": {
        "url": "https://mp.toutiao.com/mp/agw/creator_center/draft_count?type=0&app_id=1231",
        "headers": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
            "Accept": "application/json, text/plain, */*",
            "Referer": "https://mp.toutiao.com/profile_v4/manage/draft",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cookie": f"sessionid={SESSIONID}"
        }
    }
}

def extract_user_info_from_html(html_content):
    """从HTML中提取用户信息，包括禁言状态"""
    user_info = {}
    try:
        # 查找用户信息的JSON数据
        user_pattern = r'"user":\s*\{[^}]*"id":\s*"?(\d+)"?[^}]*\}'
        user_match = re.search(user_pattern, html_content)
        
        if user_match:
            # 找到user对象的起始位置
            search_start = user_match.start()
            
            # 提取用户ID
            id_pattern = r'"id":\s*"?(\d+)"?'
            id_match = re.search(id_pattern, html_content[search_start:])
            if id_match:
                user_info['id'] = id_match.group(1)
            
            # 提取昵称
            name_pattern = r'"screen_name":\s*"([^"]*)"'
            name_match = re.search(name_pattern, html_content[search_start:])
            if name_match:
                user_info['screen_name'] = name_match.group(1)
            
            # 提取实名状态
            auth_pattern = r'"has_already_authentication":\s*(true|false)'
            auth_match = re.search(auth_pattern, html_content[search_start:])
            if auth_match:
                user_info['has_already_authentication'] = auth_match.group(1) == 'true'
            
            # 提取禁言状态
            ban_pattern = r'"is_banned":\s*(true|false)'
            ban_match = re.search(ban_pattern, html_content[search_start:])
            if ban_match:
                user_info['is_banned'] = ban_match.group(1) == 'true'
                
    except Exception as e:
        print(f"解析用户信息时出错: {e}")
    
    return user_info

def make_single_request(api_name, config, iteration):
    """发起单个API请求"""
    start_time = time.time()
    try:
        response = requests.get(config["url"], headers=config["headers"], timeout=10)
        end_time = time.time()
        response_time = round((end_time - start_time) * 1000, 2)  # 毫秒
        
        result = {
            "iteration": iteration,
            "api_name": api_name,
            "status_code": response.status_code,
            "response_time_ms": response_time,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
            "success": response.status_code == 200,
            "data": None,
            "error": None
        }
        
        if response.status_code == 200:
            content_type = response.headers.get('content-type', '')
            
            if 'application/json' in content_type:
                # JSON响应
                data = response.json()
                result["data"] = data
                
                # 提取关键信息
                if api_name == "收益数据":
                    if data.get("code") == 0 and data.get("data"):
                        income_data = data["data"]
                        result["summary"] = {
                            "总收益": income_data.get("total_income", "N/A"),
                            "昨日收益": income_data.get("yesterday_income", "N/A"),
                            "可提现": income_data.get("can_withdraw_amount", "N/A")
                        }
                elif api_name == "信用分和粉丝数":
                    if data.get("code") == 0 and data.get("data"):
                        benefit_data = data["data"]
                        result["summary"] = {
                            "信用分": benefit_data.get("credit_score", "N/A"),
                            "粉丝数": benefit_data.get("fans_num", "N/A")
                        }
                elif api_name == "草稿箱数量":
                    result["summary"] = {
                        "草稿箱数量": data.get("count", data.get("data", "N/A"))
                    }
                elif api_name == "阅读量数据":
                    if data.get("code") == 0 and data.get("data"):
                        read_data = data["data"]
                        result["summary"] = {
                            "总阅读量": read_data.get("total_read_play_count", "N/A"),
                            "昨日阅读量": read_data.get("yesterday_read_count", "N/A")
                        }
            else:
                # HTML响应（主要是账号详细信息）
                if api_name == "账号详细信息":
                    user_info = extract_user_info_from_html(response.text)
                    result["data"] = {"html_length": len(response.text), "user_info": user_info}
                    result["summary"] = {
                        "用户ID": user_info.get("id", "N/A"),
                        "昵称": user_info.get("screen_name", "N/A"),
                        "实名状态": "已实名" if user_info.get("has_already_authentication") else "未实名",
                        "禁言状态": "已禁言" if user_info.get("is_banned") else "正常"
                    }
                else:
                    result["data"] = {"html_length": len(response.text)}
        else:
            result["error"] = f"HTTP {response.status_code}: {response.text[:200]}"
            
    except requests.exceptions.RequestException as e:
        end_time = time.time()
        response_time = round((end_time - start_time) * 1000, 2)
        result = {
            "iteration": iteration,
            "api_name": api_name,
            "status_code": None,
            "response_time_ms": response_time,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
            "success": False,
            "data": None,
            "error": str(e)
        }
    
    return result

def run_concurrent_test():
    """运行并发测试"""
    print(f"🚀 开始并发测试")
    print(f"📊 测试配置:")
    print(f"   - SessionID: {SESSIONID}")
    print(f"   - Homepage URL: {HOMEPAGE_URL}")
    print(f"   - 测试次数: {TEST_ITERATIONS}")
    print(f"   - 间隔时间: {INTERVAL_MS}ms")
    print(f"   - API数量: {len(API_ENDPOINTS)}")
    print(f"   - 总请求数: {TEST_ITERATIONS * len(API_ENDPOINTS)}")
    print("=" * 80)
    
    all_results = []
    
    for iteration in range(1, TEST_ITERATIONS + 1):
        print(f"\n🔄 第 {iteration}/{TEST_ITERATIONS} 轮测试 - {datetime.now().strftime('%H:%M:%S')}")
        
        # 使用线程池并发执行所有API请求
        with ThreadPoolExecutor(max_workers=len(API_ENDPOINTS)) as executor:
            # 提交所有任务
            future_to_api = {
                executor.submit(make_single_request, api_name, config, iteration): api_name
                for api_name, config in API_ENDPOINTS.items()
            }
            
            # 收集结果
            iteration_results = []
            for future in as_completed(future_to_api):
                api_name = future_to_api[future]
                try:
                    result = future.result()
                    iteration_results.append(result)
                    
                    # 实时输出结果
                    status = "✅" if result["success"] else "❌"
                    summary = result.get("summary", {})
                    summary_str = " | ".join([f"{k}: {v}" for k, v in summary.items()]) if summary else "无数据"
                    
                    print(f"  {status} {api_name}: {result['response_time_ms']}ms - {summary_str}")
                    
                except Exception as e:
                    print(f"  ❌ {api_name}: 执行异常 - {e}")
            
            all_results.extend(iteration_results)
        
        # 间隔等待（除了最后一次）
        if iteration < TEST_ITERATIONS:
            time.sleep(INTERVAL_MS / 1000.0)
    
    return all_results

def analyze_results(results):
    """分析测试结果"""
    print("\n" + "=" * 80)
    print("📈 测试结果分析")
    print("=" * 80)
    
    # 按API分组统计
    api_stats = {}
    for result in results:
        api_name = result["api_name"]
        if api_name not in api_stats:
            api_stats[api_name] = {
                "total": 0,
                "success": 0,
                "failed": 0,
                "response_times": [],
                "errors": []
            }
        
        api_stats[api_name]["total"] += 1
        if result["success"]:
            api_stats[api_name]["success"] += 1
            api_stats[api_name]["response_times"].append(result["response_time_ms"])
        else:
            api_stats[api_name]["failed"] += 1
            if result["error"]:
                api_stats[api_name]["errors"].append(result["error"])
    
    # 输出统计结果
    for api_name, stats in api_stats.items():
        print(f"\n🔍 {api_name}:")
        print(f"   总请求数: {stats['total']}")
        print(f"   成功数: {stats['success']} ({stats['success']/stats['total']*100:.1f}%)")
        print(f"   失败数: {stats['failed']} ({stats['failed']/stats['total']*100:.1f}%)")
        
        if stats["response_times"]:
            avg_time = sum(stats["response_times"]) / len(stats["response_times"])
            min_time = min(stats["response_times"])
            max_time = max(stats["response_times"])
            print(f"   响应时间: 平均{avg_time:.1f}ms, 最小{min_time:.1f}ms, 最大{max_time:.1f}ms")
        
        if stats["errors"]:
            print(f"   错误类型: {set(stats['errors'])}")
    
    # 总体统计
    total_requests = len(results)
    total_success = sum(1 for r in results if r["success"])
    total_failed = total_requests - total_success
    
    print(f"\n📊 总体统计:")
    print(f"   总请求数: {total_requests}")
    print(f"   成功数: {total_success} ({total_success/total_requests*100:.1f}%)")
    print(f"   失败数: {total_failed} ({total_failed/total_requests*100:.1f}%)")
    
    # 检查是否有请求限制
    if total_failed > 0:
        print(f"\n⚠️  检测到 {total_failed} 个失败请求，可能存在请求限制或其他问题")
    else:
        print(f"\n✅ 所有请求均成功，本地环境暂未发现明显的请求限制")

if __name__ == "__main__":
    try:
        # 运行测试
        results = run_concurrent_test()
        
        # 分析结果
        analyze_results(results)
        
        # 保存详细结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"测试结果_{timestamp}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 详细结果已保存到: {filename}")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
